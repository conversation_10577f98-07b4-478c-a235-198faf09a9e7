/* GLOBAL */
html,
body {
  font-family: 'Work Sans', sans-serif;
}

.heading {
  text-align: center;
  margin-bottom: 3rem;
}

.heading h2 {
  font-size: 40px;
  color: #ff4f81;
  font-weight: bold;
  letter-spacing: 0.5px;
}

.heading p {
  text-transform: capitalize;
  letter-spacing: 4px;
  color: black;
  font-size: 17px;
}

.heading p::after {
  content: '';
  width: 70px;
  height: 2px;
  background-color: black;
  display: block;
  margin: 20px auto;
}

.heading--white p {
  color: white;
}

.section {
  padding: 5rem 0;
}

/* HEADER */
.header__top {
  background-color: #ff4f81;
  font-size: 14px;
}

.header__top a {
  text-decoration: none;
  color: white;
  transition: all 0.5s;
}

.header__right a {
  width: 30px;
  height: 30px;
  background-color: black;
  border: 1px solid black;
  display: inline-block;

  text-align: center;
  line-height: 30px;
  margin-left: 0.25rem;
}

.header__right a:hover {
  background-color: #ff4f81;
  border-color: white;
}

.header__left a:hover {
  color: black;
}

.navInstruction a {
  color: black;
}

.navInstruction .navbar-brand p:first-child {
  font-size: 36px;
}

.navInstruction .navbar-brand p:first-child i {
  transform: skewX(-10deg);
}

.navInstruction .navbar-brand p:last-child {
  font-size: 11px;
  color: #ff4f81;
  letter-spacing: 3px;
  font-weight: bold;
}

#navbarInstruction .nav-item {
  margin: 0 15px;
}


#navbarInstruction .nav-item::after {
  content: '';
  width: 0;
  height: 2px;
  background-color: #ff4f81;
  display: block;
  transition: all 0.5s;
}

#navbarInstruction .nav-item:hover::after {
  width: 100%;
}

#navbarInstruction .nav-item:active::after {
  width: 100%;
}

#navbarInstruction .nav-link {
  font-size: 14px;
  font-weight: bold;
  letter-spacing: 1px;
  transition: all 0.5s;
}

#navbarInstruction .nav-item:hover .nav-link {
  color: #ff4f81
}

#navbarInstruction .nav-item.active .nav-link {
  color: #ff4f81
}


#navbarInstruction .dropdown-menu {
  background-color: rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(0, 0, 0, 0.6);
}

#navbarInstruction .dropdown-item {
  color: white;
  transition: all 0.5s;
}


#navbarInstruction .dropdown-item:focus,
#navbarInstruction .drodown-item:hover {
  background-color: transparent;
  color: #ff4f81;
}

/* Carousel  */
#carsouselCyber .carousel-caption {
  top: 55%;
  left: 50%;

  transform: translate(-50%, -50%)
}

#carsouselCyber .carousel-caption h1 {
  font-size: 52px;
}


#carsouselCyber .carousel-caption p {
  letter-spacing: 4px;
  line-height: 1.8rem;

  width: 70%;
  margin: 0 auto;

  margin-bottom: 25px;
  font-weight: 300;
}


#carsouselCyber .carousel-caption button {
  padding: 11px 20px;
  color: white;
  border: 1px solid white;
  border-radius: 0;
  letter-spacing: 2px;
  font-size: 14px;
  transition: all 0.5s;
}


#carsouselCyber .carousel-caption button:hover {
  background-color: #ff4f81;
  border-color: #ff4f81;
}

/* WELCOME */
.welcome__content>p {
  width: 80%;
  margin: 0 auto;

  text-align: center;
  font-size: 15px;
  /* 1ne = 15px */
  line-height: 1.8em;
  color: #999;
  letter-spacing: 1px;
  padding: 1rem 0 5rem;
}

.welcome__item i {
  font-size: 2rem;
}

.welcome__item h3 {
  color: #ff4f81;
  font-weight: bold;
  font-size: 1.8rem;
}

.welcome__item h4 {
  color: black;
  font-weight: bold;
  font-size: 1rem;
  margin: 1rem 0 0.2rem;
}

.welcome__item p {
  color: #999;
  font-size: 15px;
  line-height: 1.8em;
  margin: 10px 0 0;
  letter-spacing: 1px;
}

.welcome__col {
  padding: 2rem;
}

/* OUR Stats */
.ourStats {
  background-image: url(../img/banner2.jpg);

  background-attachment: fixed;
  background-size: cover;
}

.ourStats__content {
  text-align: center;
  color: white;
}

.ourStats__item i {
  font-size: 2.5rem;
}

.ourStats__item h3 {
  font-size: 1.3rem;
  letter-spacing: 3px;
  font-weight: bold;
  margin: 30px 0 20px;
  color: white;
}

.ourStats__item p {
  font-size: 3rem;
  letter-spacing: 2px;
  font-weight: 300;
}

/* OFFER */
.offer__col {
  padding: 15px;
}

.offer__item {
  padding: 25px 40px;
  box-shadow: 0px 1px 8px 0px rgba(158, 158, 158, 0.75);
  text-align: center;
  background-color: white;
  transition: all 0.5s;
}

.offer__item:hover {
  background-color: #12ccc5;
}

.offer__item i {
  color: #ff4f81;
  font-size: 40px;
}

.offer__item h3 {
  font-size: 20px;
  color: black;
  margin: 25px 0 20px;
}

.offer__item p {
  color: #6b6b6b;
  font-size: 14px;
  letter-spacing: 0.5px;
}

/* NEWS */
.news__content img {
  border: 20px solid #dedede;
}

.news__content h3 {
  font-size: 1.2rem;
  font-weight: bold;
}

.news__content a {
  text-decoration: none;
  color: black;
  letter-spacing: 1px;
  transition: all 0.5s;
}

.news__content a:hover {
  color: #ff4f81;
}

.news__content h4 {
  font-size: 1rem;
  font-weight: bold;
  letter-spacing: 1px;
  color: #ff4f81;
  margin: 1rem 0;
}

.news__content p {
  color: #999;
  margin-top: 1rem;
  line-height: 1.8rem;
  font-size: 0.9rem;
}

.news__content .row:last-child {
  margin-top: 5rem;
}

/* FOOTER */
footer {
  background-color: #141415;
}

.footer__top h3 {
  font-size: 24px;
  font-weight: bold;
  color: #ff4f81;
  letter-spacing: 1px;
  margin-bottom: 1.5rem;
}

.footer__top p {
  color: #8b8b96;
  font-size: 15px;
  line-height: 2em;
  margin-bottom: 2rem;
  letter-spacing: 1px;
}

.footer__link,
.footer__contact {
  margin-bottom: 1rem;
}

.footer__link i {
  color: #ff4f81;
  margin-right: 1rem;
}

.footer__link a,
.footer__contact a {
  color: #8b8b96;
  text-decoration: none;
  font-size: 15px;
  line-height: 1.5em;
  letter-spacing: 1px;
  transition: all 0.5s;
}

.footer__link a:hover,
.footer__contact a:hover {
  color: white;
}

.footer__contact i {
  width: 32px;
  height: 32px;
  border: 1px solid #4b4b50;
  color: white;
  text-align: center;
  line-height: 32px;
  margin-right: 1rem;

}

.footer__form {
  margin-top: 3rem;
}

.footer__form label {
  color: #ff4f81;
  font-size: 24px;
  font-weight: bold;
  letter-spacing: 1px;
}

.footer__form input {
  padding: 14px;
  border-radius: 0;
  border: none;
  letter-spacing: 1px;
  color: #888888;
  height: auto;
}

.footer__form button {
  background-color: #FF4F81;
  padding: 12px 35px;
  letter-spacing: 1px;
  border-radius: 0;
  color: white;
  outline: none;
  transition: all 0.5s;
}

.footer__form button:hover {
  background-color: #ec1955;
  color: white;
}

.footer__bottom {
  background-color: #0F0F10;
  text-align: center;
  padding: 1rem 0;
  letter-spacing: 1px;
  color: white;
}

.footer__bottom a {
  text-decoration: none;
  color: #FF4F81;
  transition: all 0.5s;
}

.footer__bottom a:hover {
  color: white;
}

/* MODAL */
#cyberModal h5 {
  font-size: 29px;
  color: #FF4F81;
  font-weight: bold;
}

#cyberModal p {
  font-size: 14px;
  color: #777;
  line-height: 1.8em;
  padding: 20px 0;
}

#cyberModal .modal-dialog {
  /* They do for responsive modal */
  max-width: 600px;
}


/******************************************
************** RESPONSIVE *****************
******************************************/

@media screen and (max-width:991.98px) {

  /* HEADER */
  .navbarInstruction .navbar-brand p:first-child {
    font-size: 3vw;
  }

  .navbarInstruction .navbar-brand p:last-child {
    font-size: 1.5vw;
  }
}

@media screen and (max-width:767.98px) {

  /* HEADER */
  #navbarInstruction .navbar-nav {
    background-color: #eee;
    text-align: center;
  }

  #navbarInstruction .dropdown-menu {
    background-color: #e0e1e2;
    border-color: #e0e1e2;
    text-align: center;
  }

  #navbarInstruction .dropdown-item {
    color: black;
  }
}

@media screen and (min-width:768px) and (max-width:992px) {

  /* HEADER */
  #navbarInstruction .nav-item {
    margin: 0 2px;
    padding: 0 2px;
  }

  #navbarInstruction .nav-link {
    font-size: 12px;
    padding: 5px;
  }
}