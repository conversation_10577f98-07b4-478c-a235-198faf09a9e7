// Click Effects JavaScript
document.addEventListener('DOMContentLoaded', function() {

    // Ripple effect for buttons
    function createRipple(event) {
        const button = event.currentTarget;
        const circle = document.createElement("span");
        const diameter = Math.max(button.clientWidth, button.clientHeight);
        const radius = diameter / 2;

        circle.style.width = circle.style.height = `${diameter}px`;
        circle.style.left = `${event.clientX - button.offsetLeft - radius}px`;
        circle.style.top = `${event.clientY - button.offsetTop - radius}px`;
        circle.classList.add("ripple");

        const ripple = button.getElementsByClassName("ripple")[0];
        if (ripple) {
            ripple.remove();
        }

        button.appendChild(circle);
    }

    // Add ripple effect to all buttons
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('click', createRipple);
    });

    // Particle explosion effect for offer items
    function createParticles(event) {
        const rect = event.currentTarget.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;

        for (let i = 0; i < 8; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.cssText = `
                position: fixed;
                width: 6px;
                height: 6px;
                background: #ff4f81;
                border-radius: 50%;
                pointer-events: none;
                z-index: 1000;
                left: ${centerX}px;
                top: ${centerY}px;
            `;

            document.body.appendChild(particle);

            const angle = (i / 8) * Math.PI * 2;
            const velocity = 100;
            const vx = Math.cos(angle) * velocity;
            const vy = Math.sin(angle) * velocity;

            let x = centerX;
            let y = centerY;
            let opacity = 1;

            const animate = () => {
                x += vx * 0.02;
                y += vy * 0.02;
                opacity -= 0.02;

                particle.style.left = x + 'px';
                particle.style.top = y + 'px';
                particle.style.opacity = opacity;

                if (opacity > 0) {
                    requestAnimationFrame(animate);
                } else {
                    particle.remove();
                }
            };

            requestAnimationFrame(animate);
        }
    }

    // Add particle effect to offer items
    const offerItems = document.querySelectorAll('.offer__item');
    offerItems.forEach(item => {
        item.addEventListener('click', createParticles);
    });

    // Counter animation for stats
    function animateCounter(element, target) {
        let current = 0;
        const increment = target / 50;
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            element.textContent = Math.floor(current);
        }, 30);
    }

    // Add click animation to stats
    const statsItems = document.querySelectorAll('.ourStats__item');
    statsItems.forEach(item => {
        const number = item.querySelector('p');
        const originalValue = parseInt(number.textContent);

        item.addEventListener('click', () => {
            number.textContent = '0';
            animateCounter(number, originalValue);
        });
    });

    // Floating text effect
    function createFloatingText(event, text) {
        const floatingText = document.createElement('div');
        floatingText.textContent = text;
        floatingText.style.cssText = `
            position: fixed;
            left: ${event.clientX}px;
            top: ${event.clientY}px;
            color: #ff4f81;
            font-weight: bold;
            font-size: 14px;
            pointer-events: none;
            z-index: 1000;
            transition: all 1s ease;
        `;

        document.body.appendChild(floatingText);

        setTimeout(() => {
            floatingText.style.transform = 'translateY(-50px)';
            floatingText.style.opacity = '0';
        }, 100);

        setTimeout(() => {
            floatingText.remove();
        }, 1100);
    }

    // Add floating text to navigation items
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        item.addEventListener('click', (event) => {
            const text = item.querySelector('.nav-link').textContent;
            createFloatingText(event, `✨ ${text}`);
        });
    });

    // Add floating text to social media icons
    const socialIcons = document.querySelectorAll('.header__right a');
    const socialTexts = ['Facebook', 'Twitter', 'Pinterest', 'LinkedIn'];
    socialIcons.forEach((icon, index) => {
        icon.addEventListener('click', (event) => {
            event.preventDefault();
            createFloatingText(event, `📱 ${socialTexts[index]}`);
        });
    });

    // Color wave effect for welcome items
    function createColorWave(element) {
        element.style.background = 'linear-gradient(45deg, #ff4f81, #12ccc5, #ff4f81)';
        element.style.backgroundSize = '300% 300%';
        element.style.animation = 'colorWave 2s ease';

        setTimeout(() => {
            element.style.background = '';
            element.style.animation = '';
        }, 2000);
    }

    // Add color wave to welcome items
    const welcomeItems = document.querySelectorAll('.welcome__item');
    welcomeItems.forEach(item => {
        item.addEventListener('click', () => {
            createColorWave(item.parentElement);
        });
    });

    // Add CSS for animations
    const style = document.createElement('style');
    style.textContent = `
        .ripple {
            position: absolute;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.6);
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
        }

        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        @keyframes colorWave {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .particle {
            animation: particleFade 1s ease-out forwards;
        }

        @keyframes particleFade {
            to {
                opacity: 0;
                transform: scale(0);
            }
        }
    `;
    document.head.appendChild(style);

    // Add click sound effect (optional)
    function playClickSound() {
        // Create audio context for click sound
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.1);

        gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.1);
    }

    // Add sound to important buttons (optional - uncomment to enable)
    // buttons.forEach(button => {
    //     button.addEventListener('click', playClickSound);
    // });

    // Screen size detection functions
    function isMobileSize() {
        return window.innerWidth < 576;
    }

    function isSmallTabletSize() {
        return window.innerWidth >= 576 && window.innerWidth < 768;
    }

    function isTabletSize() {
        return window.innerWidth >= 768 && window.innerWidth < 992;
    }

    function isDesktopSize() {
        return window.innerWidth >= 992 && window.innerWidth < 1200;
    }

    function isLargeDesktopSize() {
        return window.innerWidth >= 1200;
    }

    function getCurrentScreenSize() {
        if (isMobileSize()) return 'mobile';
        if (isSmallTabletSize()) return 'small-tablet';
        if (isTabletSize()) return 'tablet';
        if (isDesktopSize()) return 'desktop';
        if (isLargeDesktopSize()) return 'large-desktop';
        return 'unknown';
    }

    // Adaptive particle effect based on screen size
    function createAdaptiveParticles(event) {
        const screenSize = getCurrentScreenSize();
        let particleCount, particleSize, velocity;

        switch(screenSize) {
            case 'mobile':
                particleCount = 6;
                particleSize = 4;
                velocity = 80;
                break;
            case 'small-tablet':
                particleCount = 8;
                particleSize = 6;
                velocity = 100;
                break;
            case 'tablet':
                particleCount = 12;
                particleSize = 8;
                velocity = 120;
                break;
            case 'desktop':
                particleCount = 16;
                particleSize = 10;
                velocity = 140;
                break;
            case 'large-desktop':
                particleCount = 20;
                particleSize = 12;
                velocity = 160;
                break;
            default:
                particleCount = 8;
                particleSize = 6;
                velocity = 100;
        }

        const rect = event.currentTarget.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;

        // Create adaptive particles
        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            particle.className = 'adaptive-particle';
            particle.style.cssText = `
                position: fixed;
                width: ${particleSize}px;
                height: ${particleSize}px;
                background: linear-gradient(45deg, #ff4f81, #12ccc5);
                border-radius: 50%;
                pointer-events: none;
                z-index: 1000;
                left: ${centerX}px;
                top: ${centerY}px;
                box-shadow: 0 0 ${particleSize}px rgba(255, 79, 129, 0.8);
            `;

            document.body.appendChild(particle);

            const angle = (i / particleCount) * Math.PI * 2;
            const vx = Math.cos(angle) * velocity;
            const vy = Math.sin(angle) * velocity;

            let x = centerX;
            let y = centerY;
            let opacity = 1;
            let scale = 1;

            const animate = () => {
                x += vx * 0.025;
                y += vy * 0.025;
                opacity -= 0.015;
                scale -= 0.015;

                particle.style.left = x + 'px';
                particle.style.top = y + 'px';
                particle.style.opacity = opacity;
                particle.style.transform = `scale(${scale})`;

                if (opacity > 0) {
                    requestAnimationFrame(animate);
                } else {
                    particle.remove();
                }
            };

            requestAnimationFrame(animate);
        }
    }

    // Wave effect for medium screens
    function createWaveEffect(element) {
        if (!isTabletSize()) return;

        const wave = document.createElement('div');
        wave.className = 'wave-effect';
        wave.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent, rgba(255, 79, 129, 0.1), transparent);
            pointer-events: none;
            z-index: 1;
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        `;

        element.style.position = 'relative';
        element.style.overflow = 'hidden';
        element.appendChild(wave);

        setTimeout(() => {
            wave.style.transform = 'translateX(100%)';
        }, 50);

        setTimeout(() => {
            wave.remove();
        }, 650);
    }

    // Enhanced click effects for medium screens
    if (isTabletSize()) {
        // Enhanced offer items
        offerItems.forEach(item => {
            item.addEventListener('click', (event) => {
                createEnhancedParticles(event);
                createWaveEffect(item);
            });
        });

        // Enhanced welcome items
        welcomeItems.forEach(item => {
            item.addEventListener('click', () => {
                createWaveEffect(item.parentElement);
            });
        });

        // Enhanced stats with number animation
        statsItems.forEach(item => {
            item.addEventListener('click', () => {
                const icon = item.querySelector('i');
                icon.style.color = '#ff4f81';
                icon.style.textShadow = '0 0 10px rgba(255, 79, 129, 0.8)';

                setTimeout(() => {
                    icon.style.color = '';
                    icon.style.textShadow = '';
                }, 1000);
            });
        });
    }

    // Responsive particle adjustment
    window.addEventListener('resize', () => {
        // Clear any existing enhanced effects when screen size changes
        document.querySelectorAll('.enhanced-particle, .wave-effect').forEach(el => {
            el.remove();
        });
    });

    // Enhanced floating text for medium screens
    function createEnhancedFloatingText(event, text) {
        if (!isTabletSize()) return;

        const floatingText = document.createElement('div');
        floatingText.innerHTML = `<span style="font-size: 16px;">${text}</span>`;
        floatingText.style.cssText = `
            position: fixed;
            left: ${event.clientX}px;
            top: ${event.clientY}px;
            color: #ff4f81;
            font-weight: bold;
            pointer-events: none;
            z-index: 1000;
            transition: all 1.2s ease;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        `;

        document.body.appendChild(floatingText);

        setTimeout(() => {
            floatingText.style.transform = 'translateY(-60px) scale(1.2)';
            floatingText.style.opacity = '0';
        }, 100);

        setTimeout(() => {
            floatingText.remove();
        }, 1300);
    }

    // Apply enhanced floating text for medium screens
    if (isTabletSize()) {
        navItems.forEach(item => {
            item.addEventListener('click', (event) => {
                const text = item.querySelector('.nav-link').textContent;
                createEnhancedFloatingText(event, `✨ ${text} ✨`);
            });
        });
    }

    console.log('🎉 Click effects loaded successfully!');
    console.log(`📱 Screen size: ${window.innerWidth}px`);
    if (isTabletSize()) {
        console.log('🖥️ Enhanced effects for medium screens activated!');
    }
});
